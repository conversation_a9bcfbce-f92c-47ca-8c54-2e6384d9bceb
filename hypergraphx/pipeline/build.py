import os
import asyncio
from dataclasses import dataclass
from typing import List, Dict, Any

from hypergraphx.llm.tokenizer import Tokenizer
from hypergraphx.llm.openai_client import OpenAIClient
from hypergraphx.utils.hash import compute_content_hash
from hypergraphx.utils.log import logger
from hypergraphx.utils.file import read_file
from hypergraphx.storage.json_kv_storage import JsonKVStorage

from hypergraphx.storage.networkx_hyper_storage import NetworkXHyperStorage
from hypergraphx.core.merge import (
    merge_hyperedges_then_upsert,
    merge_nodes_then_upsert,
    merge_edges_then_upsert,
)


@dataclass
class BuildConfig:
    input_file: str
    tokenizer: str = "cl100k_base"
    chunk_size: int = 1024
    chunk_overlap: int = 100
    working_dir: str = os.path.join(os.path.dirname(os.path.dirname(__file__)), "..", "cache")
    request_concurrency: int = 20
    storage_concurrency: int = 8
    do_summary: bool = False
    flush_every_n: int = 0  # 每 N 个 chunk 自动 flush（0 表示关闭）


async def _llm_extract_stub(text: str, *_, **__):
    # 占位：保持签名兼容
    return text


async def _process_single_chunk(chunk_id: str, content: str, llm_func) -> Dict[str, Any]:
    # 假设 content 已经是结构化记录文本（或 llm_func 将其转换为结构化记录文本）
    final_result = await llm_func(content, max_tokens=1024)
    return {"chunk_id": chunk_id, "content": final_result}


async def build_hypergraph(cfg: BuildConfig, llm_client: OpenAIClient | None = None, llm_func=None):
    # 1) 准备存储
    os.makedirs(cfg.working_dir, exist_ok=True)
    text_chunks_kv = JsonKVStorage(cfg.working_dir, namespace="text_chunks")
    full_docs_kv = JsonKVStorage(cfg.working_dir, namespace="full_docs")
    hg = NetworkXHyperStorage(cfg.working_dir)

    # 2) 读取原始数据并分块
    tokenizer = Tokenizer(model_name=cfg.tokenizer)
    data = read_file(cfg.input_file)

    new_docs: Dict[str, Dict[str, Any]] = {
        compute_content_hash(doc["content"], prefix="doc-"): {"content": doc["content"]}
        for doc in data
    }
    add_doc_keys = await full_docs_kv.filter_keys(list(new_docs.keys()))
    new_docs = {k: v for k, v in new_docs.items() if k in add_doc_keys}

    inserting_chunks: Dict[str, Dict[str, Any]] = {}
    for doc_key, doc in new_docs.items():
        chunks = {
            compute_content_hash(dp["content"], prefix="chunk-"): {
                **dp,
                "full_doc_id": doc_key,
            }
            for dp in tokenizer.chunk_by_token_size(
                doc["content"], cfg.chunk_overlap, cfg.chunk_size # Todo: 改成contextual chunk
            )
        }
        inserting_chunks.update(chunks)

    add_chunk_keys = await text_chunks_kv.filter_keys(list(inserting_chunks.keys()))
    inserting_chunks = {k: v for k, v in inserting_chunks.items() if k in add_chunk_keys}

    await full_docs_kv.upsert(new_docs)
    await text_chunks_kv.upsert(inserting_chunks)

    if not inserting_chunks:
        logger.warning("No new chunks to process.")
        return hg

    # 3) 抽取：并发限制
    sem = asyncio.Semaphore(cfg.request_concurrency)

    async def _run_one(k: str, v: Dict[str, Any]):
        async with sem:
            return await _process_single_chunk(k, v["content"], llm_func or _llm_extract_stub)

    processed = await asyncio.gather(*[_run_one(k, v) for k, v in inserting_chunks.items()])

    # 4) 解析 + 合并（JSON 模式）：假设 processed[i]["content"] 为 JSON 记录文本
    #    支持 JSON Lines 或 JSON 数组。解析后输出两个容器供合并：
    #    maybe_hyperedges: Dict[str, List[{weight, source_id}]]
    #    maybe_entities: Dict[str, List[{entity_type, description, source_id, hyper_relation, weight}]]

    from hypergraphx.core.parser import parse_json_records

    # 聚合所有 chunk 的记录
    from collections import defaultdict

    agg_hyperedges: Dict[str, List[Dict[str, Any]]] = defaultdict(list)
    agg_entities: Dict[str, List[Dict[str, Any]]] = defaultdict(list)

    for pack in processed:
        content = pack["content"]
        maybe_h, maybe_e = parse_json_records(content)
        for k, v in maybe_h.items():
            agg_hyperedges[k].extend(v)
        for k, v in maybe_e.items():
            agg_entities[k].extend(v)

    # 5) 写入图：并发限制 + 周期性 flush
    storage_sem = asyncio.Semaphore(cfg.storage_concurrency)

    async def _merge_h(hname: str, lst: List[Dict[str, Any]]):
        async with storage_sem:
            return await merge_hyperedges_then_upsert(hname, lst, hg)

    async def _merge_n(ename: str, lst: List[Dict[str, Any]]):
        async with storage_sem:
            return await merge_nodes_then_upsert(ename, lst, hg, do_summary=cfg.do_summary, llm_func=(llm_client.generate_answer if llm_client else None))

    async def _merge_e(ename: str, lst: List[Dict[str, Any]]):
        async with storage_sem:
            return await merge_edges_then_upsert(ename, lst, hg)

    # 批量写入，分段 flush；避免一次性聚合过大，提升稳定性
    # 将实体按批次切分，每批写入后选择性 flush
    def _batch_items(items: List[tuple], n: int) -> List[List[tuple]]:
        if n <= 0:
            return [items]
        return [items[i:i + n] for i in range(0, len(items), n)]

    # hyperedges 与 entities 分别分批
    h_batches = _batch_items(list(agg_hyperedges.items()), cfg.flush_every_n)
    e_batches = _batch_items(list(agg_entities.items()), cfg.flush_every_n)

    for hb in h_batches:
        await asyncio.gather(*[_merge_h(k, v) for k, v in hb])
        if cfg.flush_every_n > 0:
            await hg.index_done_callback()

    for eb in e_batches:
        await asyncio.gather(*[_merge_n(k, v) for k, v in eb])
        await asyncio.gather(*[_merge_e(k, v) for k, v in eb])
        if cfg.flush_every_n > 0:
            await hg.index_done_callback()
            await text_chunks_kv.index_done_callback()
            await full_docs_kv.index_done_callback()

    # 最后一次统一 flush，确保最终一致性
    await hg.index_done_callback()
    await text_chunks_kv.index_done_callback()
    await full_docs_kv.index_done_callback()

    return hg

def analyze_graph(args):
    """分析已有的超图文件"""
    print("🔍 分析超图文件...")
    
    graph_file = os.path.join(args.input, "hypergraph.graphml")
    if not os.path.exists(graph_file):
        print(f"❌ 图文件不存在: {graph_file}")
        return False
    
    try:
        import networkx as nx
        
        G = nx.read_graphml(graph_file)
        
        # 统计信息
        total_nodes = G.number_of_nodes()
        total_edges = G.number_of_edges()
        
        entities = []
        hyperedges = []
        
        for node, attrs in G.nodes(data=True):
            role = attrs.get("role", "unknown")
            if role == "entity":
                entities.append((node, attrs))
            elif role == "hyperedge":
                hyperedges.append((node, attrs))
        
        print(f"📊 图统计:")
        print(f"   - 总节点: {total_nodes}")
        print(f"   - 总边数: {total_edges}")
        print(f"   - 实体: {len(entities)}")
        print(f"   - 超边: {len(hyperedges)}")
        
        # 显示示例
        if args.show_examples:
            print(f"\n📝 实体示例 (前5个):")
            for name, attrs in entities[:5]:
                etype = attrs.get("entity_type", "N/A")
                desc = attrs.get("description", "")[:50]
                print(f"   - {name} ({etype}): {desc}...")
            
            print(f"\n🔗 超边示例 (前5个):")
            for name, attrs in hyperedges[:5]:
                weight = attrs.get("weight", "N/A")
                print(f"   - {name} (权重: {weight})")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

async def run(cfg: BuildConfig):
    # 提供默认 OpenAIClient（从环境变量读取配置）
    llm = OpenAIClient(
        model_name=os.getenv("SYNTHESIZER_MODEL") or os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
        api_key=os.getenv("SYNTHESIZER_API_KEY") or os.getenv("OPENAI_API_KEY"),
        base_url=os.getenv("SYNTHESIZER_BASE_URL") or os.getenv("OPENAI_BASE_URL"),
    )
    await build_hypergraph(cfg, llm_client=llm, llm_func=None)  # 默认 llm_func 留白，由用户自行提供


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser()
    parser.add_argument("--input_file", type=str, default="resources/input_examples/raw_demo.jsonl")
    parser.add_argument("--tokenizer", type=str, default="cl100k_base")
    parser.add_argument("--chunk_size", type=int, default=1024)
    parser.add_argument("--chunk_overlap", type=int, default=100)
    parser.add_argument("--working_dir", type=str, default="cache")
    parser.add_argument("--request_concurrency", type=int, default=20)
    parser.add_argument("--storage_concurrency", type=int, default=8)
    parser.add_argument("--do_summary", action="store_true")

    args = parser.parse_args()

    cfg = BuildConfig(
        input_file=args.input_file,
        tokenizer=args.tokenizer,
        chunk_size=args.chunk_size,
        chunk_overlap=args.chunk_overlap,
        working_dir=args.working_dir,
        request_concurrency=args.request_concurrency,
        storage_concurrency=args.storage_concurrency,
        do_summary=args.do_summary,
    )

    asyncio.run(run(cfg))

